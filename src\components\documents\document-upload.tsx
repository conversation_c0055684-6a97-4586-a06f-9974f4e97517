"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Upload } from "lucide-react";

interface DocumentUploadProps {
  onDocumentUpdate?: () => void;
}

export function DocumentUpload({ onDocumentUpdate }: DocumentUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUpload = async (files: File[]) => {
    try {
      for (const file of files) {
        // Create FormData for upload
        const formData = new FormData();
        formData.append('file', file);

        // Upload file to API
        const response = await fetch('/api/documents/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        await response.json();
        console.log(`Successfully uploaded: ${file.name}`);
      }

      // Notify parent component of update
      if (onDocumentUpdate) {
        onDocumentUpdate();
      }
    } catch (error) {
      console.error('Upload error:', error);
      throw error; // Let the enhanced file upload component handle the error
    }
  };



  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setIsUploading(true);
    try {
      await handleUpload(Array.from(files));
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      setIsUploading(true);
      try {
        await handleUpload(Array.from(files));
      } catch (error) {
        console.error('Upload failed:', error);
      } finally {
        setIsUploading(false);
      }
    }
  };

  return (
    <div className="space-y-4">
      <Card
        className={`border-2 border-dashed transition-all duration-300 cursor-pointer group ${
          isDragOver
            ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg'
            : 'border-border hover:border-primary/50 hover:bg-muted/30 hover:shadow-md'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <CardContent className="p-8 text-center">
          <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300 ${
            isDragOver
              ? 'bg-primary text-primary-foreground scale-110'
              : 'bg-muted text-muted-foreground group-hover:scale-105 group-hover:bg-primary/20'
          }`}>
            <Upload className={`w-8 h-8 transition-transform duration-300 ${isDragOver ? 'scale-110' : 'group-hover:scale-110'}`} />
          </div>

          <h3 className={`text-lg font-semibold mb-2 transition-colors duration-300 ${
            isDragOver ? 'text-primary' : 'text-foreground'
          }`}>
            {isDragOver ? '✨ Drop files here' : 'Upload your documents'}
          </h3>

          <p className="text-muted-foreground mb-4">
            Drag and drop files here, or click to browse
          </p>

          <div className="flex flex-wrap justify-center gap-2 text-sm text-muted-foreground mb-4">
            <span className="px-2 py-1 bg-muted/50 rounded-full">Max file size: 10 MB</span>
            <span className="px-2 py-1 bg-muted/50 rounded-full">Max files: 10</span>
          </div>

          <Button
            variant="outline"
            disabled={isUploading}
            className="transition-all duration-300 hover:scale-105 hover:shadow-md"
          >
            {isUploading ? (
              <>
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                Uploading...
              </>
            ) : (
              'Choose Files'
            )}
          </Button>

          <p className="text-xs text-muted-foreground mt-2">
            Supported formats: .txt, .md
          </p>
        </CardContent>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          accept=".txt,.md"
          onChange={handleFileSelect}
        />
      </Card>


    </div>
  );
}
