import { NextRequest, NextResponse } from 'next/server';
import * as cheerio from 'cheerio';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url, username, password, customHeaders } = body;

    // Validate required fields
    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return NextResponse.json(
          { error: 'Only HTTP and HTTPS URLs are supported' },
          { status: 400 }
        );
      }
    } catch {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method: 'GET',
      headers: {
        'User-Agent': 'ChatDoc URL Preview Service/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        ...customHeaders
      }
    };

    // Add authentication if provided
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      fetchOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Basic ${auth}`
      };
    }

    console.log(`Fetching preview content from URL: ${url}`);
    
    // Fetch the webpage
    const response = await fetch(url, fetchOptions);
    
    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch URL: ${response.status} ${response.statusText}` },
        { status: 400 }
      );
    }

    const contentType = response.headers.get('content-type') || '';
    if (!contentType.includes('text/html')) {
      return NextResponse.json(
        { error: `Unsupported content type: ${contentType}. Only HTML content is supported.` },
        { status: 400 }
      );
    }

    // Get HTML content
    const html = await response.text();
    
    if (!html || html.trim().length === 0) {
      return NextResponse.json(
        { error: 'No content found at the provided URL' },
        { status: 400 }
      );
    }

    // Parse HTML and extract metadata
    const $ = cheerio.load(html);

    // Extract title
    let title = $('title').text().trim();
    if (!title) {
      title = $('h1').first().text().trim();
    }
    if (!title) {
      title = new URL(url).hostname;
    }

    // Extract common content selectors for element selection
    const contentSelectors = [
      { selector: 'main', label: 'Main Content', priority: 1 },
      { selector: 'article', label: 'Article', priority: 1 },
      { selector: '[role="main"]', label: 'Main Role', priority: 1 },
      { selector: '.content', label: 'Content Class', priority: 2 },
      { selector: '#content', label: 'Content ID', priority: 2 },
      { selector: '.post-content', label: 'Post Content', priority: 2 },
      { selector: '.entry-content', label: 'Entry Content', priority: 2 },
      { selector: '.wiki-content', label: 'Wiki Content', priority: 2 },
      { selector: 'h1, h2, h3, h4, h5, h6', label: 'Headings', priority: 3 },
      { selector: 'p', label: 'Paragraphs', priority: 3 },
      { selector: 'ul, ol', label: 'Lists', priority: 3 },
      { selector: 'blockquote', label: 'Quotes', priority: 3 },
      { selector: 'pre, code', label: 'Code Blocks', priority: 3 }
    ];

    // Analyze available elements
    const availableElements = contentSelectors
      .map(({ selector, label, priority }) => {
        const elements = $(selector);
        const count = elements.length;
        const totalTextLength = elements.toArray().reduce((sum, el) => {
          return sum + $(el).text().trim().length;
        }, 0);

        return {
          selector,
          label,
          priority,
          count,
          totalTextLength,
          available: count > 0 && totalTextLength > 50
        };
      })
      .filter(item => item.available)
      .sort((a, b) => a.priority - b.priority || b.totalTextLength - a.totalTextLength);

    // Clean HTML for preview (remove scripts, styles, etc.)
    $('script, style, noscript, iframe').remove();
    $('*').removeAttr('onclick onload onerror onmouseover onmouseout');
    
    // Make all links non-functional for preview
    $('a').removeAttr('href').css('pointer-events', 'none');
    $('form').removeAttr('action method').find('input, button, textarea, select').attr('disabled', 'disabled');

    const cleanHtml = $.html();

    return NextResponse.json({
      success: true,
      title,
      html: cleanHtml,
      availableElements,
      url
    });

  } catch (error) {
    console.error('URL preview error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'URL Preview API',
    usage: 'POST with { url, username?, password?, customHeaders? }',
    supportedProtocols: ['http', 'https'],
    supportedContentTypes: ['text/html']
  });
}
