"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Send, Bot, User, Clock, AlertCircle, Wifi, WifiOff, ChevronDown, FileText, Brain } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { MarkdownRenderer } from '@/components/ui/markdown-renderer'

interface Message {
  id: string
  content: string
  sender: 'user' | 'assistant'
  timestamp: Date
  isCode?: boolean
  status?: 'sending' | 'sent' | 'error'
  thinking?: string
  sources?: Array<{
    document: string
    chunk: string
    relevance: number
  }>
}

interface ChatInterfaceProps {
  messages?: Message[]
  onSendMessage?: (message: string) => void
  isTyping?: boolean
  isConnected?: boolean
  isLoading?: boolean
  placeholder?: string
  maxHeight?: string
}

// Utility function to extract thinking content and clean message
const extractThinkingContent = (content: string): { cleanContent: string; thinking?: string } => {
  const thinkTagRegex = /<think>([\s\S]*?)<\/think>/i
  const match = content.match(thinkTagRegex)
  
  if (match) {
    const thinking = match[1].trim()
    const cleanContent = content.replace(thinkTagRegex, '').trim()
    return { cleanContent, thinking }
  }
  
  return { cleanContent: content }
}

const TypingIndicator = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="flex items-center space-x-3 px-5 py-4 bg-gray-50 dark:bg-gray-800 rounded-2xl rounded-bl-md max-w-xs shadow-sm border border-gray-200 dark:border-gray-600"
    role="status"
    aria-label="Assistant is typing"
  >
    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-700 dark:bg-gray-300 flex items-center justify-center">
      <Bot className="w-3 h-3 text-white dark:text-black" />
    </div>
    <div className="flex items-center space-x-1">
      <span className="text-sm text-gray-600 dark:text-gray-300 mr-2">AI is thinking</span>
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full"
            animate={{
              scale: [1, 1.4, 1],
              opacity: [0.4, 1, 0.4]
            }}
            transition={{
              duration: 1.2,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    </div>
  </motion.div>
)

const CollapsibleThinking = ({ thinking }: { thinking: string }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="mt-3 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
      <motion.button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 w-full text-left"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Brain className="w-3 h-3" />
        <span>View Thinking Process</span>
        <motion.div
          animate={{ rotate: isExpanded ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className="w-3 h-3" />
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            <div className="pt-2">
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 shadow-sm"
              >
                <div className="flex items-center gap-2 mb-2">
                  <Brain className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">AI Reasoning</span>
                </div>
                <div className="text-xs text-blue-700 dark:text-blue-300 whitespace-pre-wrap leading-relaxed">
                  <MarkdownRenderer content={thinking} />
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

const CollapsibleSources = ({ sources }: { sources: Array<{ document: string; chunk: string; relevance: number }> }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="mt-3 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
      <motion.button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 w-full text-left"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <FileText className="w-3 h-3" />
        <span>Sources ({sources.length})</span>
        <motion.div
          animate={{ rotate: isExpanded ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className="w-3 h-3" />
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            <div className="space-y-2 pt-2">
              {sources.map((source, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-3 shadow-sm"
                >
                  <p className="text-xs font-medium text-gray-800 dark:text-gray-200">{source.document}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-3">
                    {source.chunk}
                  </p>
                  <Badge variant="secondary" className="text-xs mt-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                    {Math.round(source.relevance * 100)}% match
                  </Badge>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

const MessageBubble = ({ message, index }: { message: Message; index: number }) => {
  const [showTimestamp, setShowTimestamp] = useState(false)
  const isUser = message.sender === 'user'

  // Extract thinking content for assistant messages
  const { cleanContent, thinking } = isUser ? { cleanContent: message.content, thinking: undefined } : extractThinkingContent(message.content)
  
  // Use extracted thinking if available, otherwise use message.thinking (for backwards compatibility)
  const displayThinking = thinking || message.thinking

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.3,
        delay: index * 0.1,
        ease: "easeOut"
      }}
      className={`flex items-end space-x-3 group ${isUser ? 'justify-end' : 'justify-start'}`}
      onMouseEnter={() => setShowTimestamp(true)}
      onMouseLeave={() => setShowTimestamp(false)}
      role="article"
      aria-label={`${message.sender} message`}
    >
      {!isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-700 dark:bg-gray-300 flex items-center justify-center mb-1 shadow-sm">
          <Bot className="w-4 h-4 text-white dark:text-black" />
        </div>
      )}
      
      <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg`}>
        <motion.div
          className={`px-4 py-3 rounded-2xl relative shadow-sm ${
            isUser
              ? 'bg-black dark:bg-gray-200 text-white dark:text-black rounded-br-md border border-gray-800 dark:border-gray-300'
              : 'bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 rounded-bl-md border border-gray-200 dark:border-gray-600'
          } ${message.isCode ? 'font-mono text-sm' : ''}`}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          {message.isCode ? (
            <pre className="whitespace-pre-wrap break-words">
              <code>{cleanContent}</code>
            </pre>
          ) : isUser ? (
            <div className="whitespace-pre-wrap break-words">{cleanContent}</div>
          ) : (
            <MarkdownRenderer content={cleanContent} />
          )}
          
          {message.status === 'sending' && (
            <motion.div
              className="absolute -bottom-1 -right-1 w-3 h-3 bg-gray-500 rounded-full shadow-sm"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            />
          )}
          
          {message.status === 'error' && (
            <AlertCircle className="absolute -bottom-1 -right-1 w-3 h-3 text-red-500" />
          )}
        </motion.div>

        {/* Thinking Process Section - Only for assistant messages */}
        {!isUser && displayThinking && (
          <CollapsibleThinking thinking={displayThinking} />
        )}

        {/* Sources */}
        {message.sources && message.sources.length > 0 && (
          <CollapsibleSources sources={message.sources} />
        )}
        
        <AnimatePresence>
          {showTimestamp && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="text-xs text-gray-500 dark:text-gray-400 mt-1 px-2"
            >
              <Clock className="w-3 h-3 inline mr-1" />
              {formatTime(message.timestamp)}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-black dark:bg-gray-200 flex items-center justify-center mb-1 shadow-sm">
          <User className="w-4 h-4 text-white dark:text-black" />
        </div>
      )}
    </motion.div>
  )
}

const EmptyState = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="flex flex-col items-center justify-center h-80 text-center space-y-4"
  >
    <div className="w-16 h-16 rounded-full bg-gray-700 dark:bg-gray-300 flex items-center justify-center shadow-lg">
      <Bot className="w-8 h-8 text-white dark:text-black" />
    </div>
    <div className="space-y-2">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Start a conversation</h3>
      <p className="text-sm text-gray-600 dark:text-gray-400 max-w-sm">
        Upload some documents and ask me anything! I&apos;m here to help with your questions.
      </p>
    </div>
  </motion.div>
)

const LoadingState = () => (
  <div className="space-y-4 p-4">
    {[1, 2, 3].map((i) => (
      <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
        <div className="space-y-2">
          <Skeleton className="h-4 w-48" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>
    ))}
  </div>
)

export default function AnimatedChatInterface({
  messages = [],
  onSendMessage = () => {},
  isTyping = false,
  isConnected = true,
  isLoading = false,
  placeholder = "Ask a question about your documents...",
  maxHeight = "100%"
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('')
  const [localMessages, setLocalMessages] = useState<Message[]>(messages)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setLocalMessages(messages)
  }, [messages])

  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [localMessages, isTyping, scrollToBottom])

  const handleSendMessage = useCallback(() => {
    if (!inputValue.trim() || !isConnected) return

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: 'user',
      timestamp: new Date(),
      status: 'sending'
    }

    setLocalMessages(prev => [...prev, newMessage])
    onSendMessage(inputValue.trim())
    setInputValue('')

    setTimeout(() => {
      setLocalMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'sent' }
            : msg
        )
      )
    }, 1000)
  }, [inputValue, isConnected, onSendMessage])

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }, [handleSendMessage])

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <div className="flex flex-col w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg" style={{ height: maxHeight }}>
      {/* Header */}
      {/* <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 px-6 py-4 rounded-t-lg"
      >
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-700 dark:bg-gray-300 rounded-full flex items-center justify-center shadow-sm">
            <Bot className="w-5 h-5 text-white dark:text-black" />
          </div>
          <div>
            <h2 className="font-semibold text-gray-800 dark:text-gray-100">RAG Assistant</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">AI-powered knowledge assistant</p>
          </div>
          <div className="ml-auto flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-600 dark:text-gray-400">Online</span>
          </div>
        </div>
      </motion.div> */}

      {/* Connection Error Alert */}
      {!isConnected && (
        <Alert className="m-4 mb-0 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20 flex-shrink-0">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700 dark:text-red-400">
            Connection lost. Please check your internet connection and try again.
          </AlertDescription>
        </Alert>
      )}

      {/* Messages Area */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <ScrollArea
          ref={scrollAreaRef}
          className="h-full p-6"
        >
          {isLoading ? (
            <LoadingState />
          ) : localMessages.length === 0 ? (
            <EmptyState />
          ) : (
            <div className="space-y-6" role="log" aria-label="Chat messages">
              <AnimatePresence>
                {localMessages.map((message, index) => (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    index={index}
                  />
                ))}
                {isTyping && (
                  <div className="flex justify-start">
                    <TypingIndicator />
                  </div>
                )}
              </AnimatePresence>
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-600 flex-shrink-0 bg-gray-50/50 dark:bg-gray-800/50 rounded-b-lg">
        <div className="flex space-x-3">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={isConnected ? placeholder : "Reconnecting..."}
            disabled={!isConnected || isLoading}
            className="flex-1 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 focus:border-gray-500 dark:focus:border-gray-400 rounded-xl"
            aria-label="Message input"
            maxLength={1000}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || !isConnected || isLoading}
            size="icon"
            aria-label="Send message"
            className="bg-black dark:bg-gray-200 hover:bg-gray-800 dark:hover:bg-gray-300 text-white dark:text-black rounded-xl shadow-sm"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <div className="flex justify-between items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <span>{inputValue.length}/1000</span>
        </div>
      </div>
    </div>
  )
}
