# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ChatDoc is a Next.js-based document chat application that allows users to upload documents (.txt, .md files) and chat with them using AI. The application uses a **Chinese AI Stack** with DeepSeek for reasoning and Doubao (ByteDance) for embeddings.

## Common Commands

### Development
- `npm run dev` - Start development server on http://localhost:3000
- `npm run build` - Build production application
- `npm run start` - Start production server
- `npm run lint` - Run ESLint code quality checks

### Environment Setup
Required environment variables in `.env.local`:
```bash
# DeepSeek API Configuration (Reasoning)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DEEPSEEK_CHAT_MODEL=deepseek-chat
DEEPSEEK_REASONING_MODEL=deepseek-r1-distill-qwen-32b-250120

# Doubao API Configuration (Embeddings)  
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
EMBEDDING_MODEL=doubao-embedding-text-240515

# Optional: OpenAI API key (backup embedding provider)
OPENAI_API_KEY=your_openai_api_key_here
```

## Architecture Overview

### Core Components
- **Frontend**: Next.js 14 with React 18, TypeScript, Tailwind CSS
- **UI Components**: Radix UI primitives with custom styling in `/src/components/ui/`
- **Document Management**: File upload, URL ingestion, and metadata tracking
- **AI Integration**: DeepSeek LLM + Doubao embeddings for RAG pipeline

### Key Services (`/src/lib/`)
- `document-manager.ts` - Handles document metadata, status tracking, and persistence
- `llamaindex-service.ts` - Main document processing and query service (simplified implementation)
- `deepseek-llm.ts` - DeepSeek API integration for chat/reasoning
- `doubao-embedding.ts` - Doubao embedding service for semantic search
- `enhanced-document-service.ts` - Advanced document processing with chunking and embeddings
- `url-document-service.ts` - Web URL content extraction and processing

### Document Processing Flow
1. **Upload/URL Ingestion**: Documents uploaded via drag-drop or fetched from URLs
2. **Processing**: Content extraction, chunking, and embedding generation
3. **Storage**: File storage in `/uploads/`, metadata in `documents_metadata.json`, chunks in `/document_chunks/`
4. **Querying**: Semantic search using embeddings + LLM reasoning for responses

### API Routes (`/src/app/api/`)
- `POST /api/documents/upload` - File upload endpoint
- `POST /api/documents/ingest-url` - URL content ingestion
- `GET /api/documents/stats` - Document statistics
- `DELETE /api/documents/[id]` - Document deletion
- `POST /api/chat` - Chat with documents endpoint

### UI Structure
- **Main Page** (`/src/app/page.tsx`): Three-column layout with document management and chat
- **Document Components** (`/src/components/documents/`): Upload, URL ingestion, document list
- **Chat Interface** (`/src/components/chat/`): AI chat with document context
- **UI Components** (`/src/components/ui/`): Reusable Radix-based components

### Data Persistence
- Documents stored in `/uploads/` directory
- Document metadata in `documents_metadata.json`
- Processed chunks in `/document_chunks/` as JSON files
- No external database - uses file system storage

### AI Integration Details
- **DeepSeek**: Primary LLM for reasoning and chat responses
- **Doubao**: Embedding generation for semantic document search
- **Fallback**: OpenAI embeddings as backup provider
- **RAG Pipeline**: Document chunking → embedding → retrieval → LLM generation

## Development Notes

### File Upload Support
- Supported formats: `.txt`, `.md` files
- Uses `formidable` for multipart form processing
- Automatic file validation and metadata extraction

### URL Content Processing
- Uses `cheerio` for HTML parsing
- `turndown` for HTML-to-Markdown conversion
- Supports various content types from web URLs

### State Management
- React state for UI components
- TanStack Query for server state management
- Document updates trigger re-renders across components

### Styling
- Tailwind CSS with custom configuration
- Framer Motion for animations
- Lucide React for icons
- Custom shadcn/ui component library