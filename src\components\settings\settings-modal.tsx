"use client";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Settings } from "lucide-react";
import { ApiKeySettings } from "./api-key-settings";

interface SettingsModalProps {
  onKeysUpdated?: () => void;
}

export function SettingsModal({ onKeysUpdated }: SettingsModalProps) {
  const handleKeysUpdated = () => {
    onKeysUpdated?.();
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Settings
        </Button>
      </DialogTrigger>
      <DialogContent className="w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Application Settings</DialogTitle>
          <DialogDescription>
            Configure your API keys and application preferences.
          </DialogDescription>
        </DialogHeader>
        <ApiKeySettings onKeysUpdated={handleKeysUpdated} />
      </DialogContent>
    </Dialog>
  );
}