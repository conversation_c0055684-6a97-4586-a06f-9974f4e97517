"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Settings, X } from "lucide-react";
import { ApiKeySettings } from "./api-key-settings";
import { createPortal } from "react-dom";

interface SettingsModalProps {
  onKeysUpdated?: () => void;
}

export function SettingsModal({ onKeysUpdated }: SettingsModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const closeButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';

      // Focus the close button when modal opens
      setTimeout(() => {
        closeButtonRef.current?.focus();
      }, 100);

      // Handle escape key
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setIsOpen(false);
        }
      };

      document.addEventListener('keydown', handleEscape);

      return () => {
        document.body.style.overflow = 'unset';
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isOpen]);

  const handleKeysUpdated = () => {
    onKeysUpdated?.();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setIsOpen(false);
    }
  };

  if (!mounted) {
    return (
      <Button variant="outline" size="sm" className="flex items-center gap-2">
        <Settings className="h-4 w-4" />
        Settings
      </Button>
    );
  }

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-2"
        onClick={() => setIsOpen(true)}
      >
        <Settings className="h-4 w-4" />
        Settings
      </Button>

      {isOpen && createPortal(
        <div
          className="fixed inset-0 flex items-center justify-center p-4 animate-in fade-in-0 duration-200"
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)'
          }}
          onClick={handleBackdropClick}
          role="dialog"
          aria-modal="true"
          aria-labelledby="settings-modal-title"
          aria-describedby="settings-modal-description"
        >
          <div
            className="relative w-full max-w-3xl max-h-[90vh] overflow-y-auto bg-background border rounded-lg shadow-2xl animate-in zoom-in-95 duration-200"
            style={{ zIndex: 10000 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="sticky top-0 bg-background border-b px-6 py-4 flex items-center justify-between">
              <div>
                <h2 id="settings-modal-title" className="text-lg font-semibold leading-none tracking-tight">
                  Application Settings
                </h2>
                <p id="settings-modal-description" className="text-sm text-muted-foreground mt-1">
                  Configure your API keys and application preferences.
                </p>
              </div>
              <button
                ref={closeButtonRef}
                onClick={() => setIsOpen(false)}
                className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 p-1"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </div>

            <div className="p-6">
              <ApiKeySettings onKeysUpdated={handleKeysUpdated} />
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
}