"use client";

import { useState, useEffect } from "react";
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Settings, X } from "lucide-react";
import { ApiKeySettings } from "./api-key-settings";
import { createPortal } from "react-dom";

interface SettingsModalProps {
  onKeysUpdated?: () => void;
}

export function SettingsModal({ onKeysUpdated }: SettingsModalProps) {
  const [open, setOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleKeysUpdated = () => {
    onKeysUpdated?.();
    // Optionally close the modal after successful key update
    // setOpen(false);
  };

  if (!mounted) {
    return (
      <Button variant="outline" size="sm" className="flex items-center gap-2">
        <Settings className="h-4 w-4" />
        Settings
      </Button>
    );
  }

  return (
    <>
      <Button 
        variant="outline" 
        size="sm" 
        className="flex items-center gap-2"
        onClick={() => setOpen(true)}
      >
        <Settings className="h-4 w-4" />
        Settings
      </Button>
      
      {open && createPortal(
        <div className="fixed inset-0 z-[9999] flex items-center justify-center">
          <div 
            className="fixed inset-0 bg-black/80 z-[9998]" 
            onClick={() => setOpen(false)}
          />
          <div className="relative z-[9999] w-full max-w-3xl max-h-[90vh] overflow-y-auto bg-background border rounded-lg shadow-lg p-6 m-4">
            <button
              onClick={() => setOpen(false)}
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
            
            <div className="mb-6">
              <h2 className="text-lg font-semibold leading-none tracking-tight mb-2">
                Application Settings
              </h2>
              <p className="text-sm text-muted-foreground">
                Configure your API keys and application preferences.
              </p>
            </div>
            
            <ApiKeySettings onKeysUpdated={handleKeysUpdated} />
          </div>
        </div>,
        document.body
      )}
    </>
  );
}