"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { File, X, CheckCircle, Clock, AlertCircle, RefreshCw } from "lucide-react";

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status?: 'uploading' | 'processing' | 'indexed' | 'error';
  errorMessage?: string;
}

interface UploadedDocumentsProps {
  onDocumentUpdate?: () => void;
  refreshTrigger?: number;
}

export function UploadedDocuments({ onDocumentUpdate, refreshTrigger }: UploadedDocumentsProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    fetchDocumentStatus();
  }, [refreshTrigger]);

  const fetchDocumentStatus = async () => {
    try {
      setIsRefreshing(true);
      const response = await fetch('/api/documents/stats');
      if (response.ok) {
        const data = await response.json();
        setUploadedFiles(data.documents.map((doc: { id: string; originalName?: string; filename: string; size: number; status: string; errorMessage?: string }) => ({
          id: doc.id,
          name: doc.originalName || doc.filename,
          size: doc.size,
          type: '',
          status: doc.status,
          errorMessage: doc.errorMessage,
        })));
      }
    } catch (error) {
      console.error('Failed to fetch document status:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleRemoveFile = async (fileId: string) => {
    try {
      const response = await fetch(`/api/documents/${fileId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
        
        if (onDocumentUpdate) {
          onDocumentUpdate();
        }
      } else {
        alert('Failed to remove file');
      }
    } catch (error) {
      console.error('Remove file error:', error);
      alert('Failed to remove file');
    }
  };

  const handleManualRefresh = () => {
    fetchDocumentStatus();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'indexed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'uploading':
        return 'Uploading...';
      case 'processing':
        return 'Processing...';
      case 'indexed':
        return 'Ready';
      case 'error':
        return 'Error';
      default:
        return 'Ready';
    }
  };

  if (uploadedFiles.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/30 flex items-center justify-center">
          <File className="w-8 h-8 opacity-50" />
        </div>
        <p className="text-sm">No files uploaded yet</p>
        <p className="text-xs mt-1 opacity-75">Upload documents to start chatting</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium flex items-center gap-2">
          {/* <div className="w-5 h-5 rounded bg-primary/10 flex items-center justify-center">
            📁
          </div> */}
          {uploadedFiles.length} {uploadedFiles.length === 1 ? 'file' : 'files'}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleManualRefresh}
          disabled={isRefreshing}
          className="text-xs"
        >
          <RefreshCw className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
      {uploadedFiles.map((file) => (
        <Card key={file.id} className="p-4 transition-all duration-200 hover:shadow-md hover:scale-[1.01] group">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-muted/50 flex items-center justify-center">
                <File className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium group-hover:text-primary transition-colors">{file.name}</p>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <span className="px-2 py-0.5 bg-muted/50 rounded-full">{formatFileSize(file.size)}</span>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(file.status)}
                    <span className={`font-medium ${
                      file.status === 'indexed' ? 'text-green-600' :
                      file.status === 'error' ? 'text-red-600' :
                      'text-blue-600'
                    }`}>
                      {getStatusText(file.status)}
                    </span>
                  </div>
                </div>
                {file.errorMessage && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-xs text-red-600">{file.errorMessage}</p>
                  </div>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveFile(file.id)}
              disabled={file.status === 'processing'}
              className="opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-50 hover:text-red-600"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      ))}
    </div>
  );
} 