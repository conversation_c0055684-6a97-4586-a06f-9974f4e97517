@import "tailwindcss";

/* Highlight.js theme for code syntax highlighting */
@import 'highlight.js/styles/github.css';

/* Tailwind CSS v4 theme configuration */
@theme {
  --color-border: 214.3 31.8% 91.4%;
  --color-input: 214.3 31.8% 91.4%;
  --color-ring: 221.2 83.2% 53.3%;
  --color-background: 0 0% 100%;
  --color-foreground: 222.2 84% 4.9%;
  --color-primary: 221.2 83.2% 53.3%;
  --color-primary-foreground: 210 40% 98%;
  --color-secondary: 210 40% 96%;
  --color-secondary-foreground: 222.2 84% 4.9%;
  --color-destructive: 0 84.2% 60.2%;
  --color-destructive-foreground: 210 40% 98%;
  --color-muted: 210 40% 96%;
  --color-muted-foreground: 215.4 16.3% 46.9%;
  --color-accent: 210 40% 96%;
  --color-accent-foreground: 222.2 84% 4.9%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 222.2 84% 4.9%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 222.2 84% 4.9%;
}

@layer base {
  body {
    background-color: hsl(var(--color-background));
    color: hsl(var(--color-foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(215.4 16.3% 46.9%) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(215.4 16.3% 46.9%);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(222.2 84% 4.9%);
  }

  /* Animation utilities */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  /* Smooth transitions for interactive elements */
  .transition-smooth {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Focus styles */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-[1.02];
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }

  /* Modal and dialog fixes */
  [data-radix-popper-content-wrapper] {
    z-index: 50 !important;
  }

  [data-radix-dialog-overlay] {
    z-index: 50 !important;
  }

  [data-radix-dialog-content] {
    z-index: 51 !important;
  }
}
