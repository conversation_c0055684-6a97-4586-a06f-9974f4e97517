"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Eye, 
  EyeOff, 
  CheckSquare, 
  Square, 
  ArrowRight, 
  Loader2, 
  AlertCircle,
  CheckCircle,
  Code,
  Globe
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface AvailableElement {
  selector: string;
  label: string;
  priority: number;
  count: number;
  totalTextLength: number;
  available: boolean;
}

interface SelectedElement {
  selector: string;
  label: string;
  selected: boolean;
}

interface ElementSelectorProps {
  url: string;
  title: string;
  html: string;
  availableElements: AvailableElement[];
  onProceed: (selectedElements: SelectedElement[]) => void;
  onCancel: () => void;
  isProcessing?: boolean;
}

export function ElementSelector({
  url,
  title,
  html,
  availableElements,
  onProceed,
  onCancel,
  isProcessing = false
}: ElementSelectorProps) {
  const [selectedElements, setSelectedElements] = useState<SelectedElement[]>([]);
  const [showPreview, setShowPreview] = useState(true);
  const [previewContent, setPreviewContent] = useState<string>('');

  // Initialize selected elements with sensible defaults
  useEffect(() => {
    const defaultSelected = availableElements.map(element => ({
      selector: element.selector,
      label: element.label,
      selected: element.priority <= 2 // Auto-select high priority elements
    }));
    setSelectedElements(defaultSelected);
  }, [availableElements]);

  // Generate preview content when selection changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        let preview = '';
        const selectedSelectors = selectedElements
          .filter(el => el.selected)
          .map(el => el.selector);

        for (const selector of selectedSelectors) {
          const elements = doc.querySelectorAll(selector);
          elements.forEach(element => {
            const text = element.textContent?.trim() || '';
            if (text.length > 20) {
              preview += text.substring(0, 200) + (text.length > 200 ? '...' : '') + '\n\n';
            }
          });
        }

        setPreviewContent(preview.trim());
      } catch (error) {
        console.error('Error generating preview:', error);
        setPreviewContent('Preview not available');
      }
    }
  }, [selectedElements, html]);

  const toggleElement = (selector: string) => {
    setSelectedElements(prev =>
      prev.map(el =>
        el.selector === selector
          ? { ...el, selected: !el.selected }
          : el
      )
    );
  };

  const selectAll = () => {
    setSelectedElements(prev =>
      prev.map(el => ({ ...el, selected: true }))
    );
  };

  const selectNone = () => {
    setSelectedElements(prev =>
      prev.map(el => ({ ...el, selected: false }))
    );
  };

  const handleProceed = () => {
    const hasSelection = selectedElements.some(el => el.selected);
    if (hasSelection) {
      onProceed(selectedElements);
    }
  };

  const selectedCount = selectedElements.filter(el => el.selected).length;
  const hasSelection = selectedCount > 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Select Content Elements</h3>
        <p className="text-sm text-muted-foreground">
          Choose which parts of the webpage to extract for your document
        </p>
        <div className="flex items-center justify-center gap-2 mt-2">
          <Globe className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground truncate max-w-md">{url}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Element Selection Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Available Elements</span>
              <Badge variant="secondary">{selectedCount} selected</Badge>
            </CardTitle>
            <CardDescription>
              Select the content elements you want to extract from the webpage
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Quick Actions */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={selectAll}
                className="flex-1"
              >
                <CheckSquare className="h-4 w-4 mr-1" />
                Select All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={selectNone}
                className="flex-1"
              >
                <Square className="h-4 w-4 mr-1" />
                Select None
              </Button>
            </div>

            {/* Element List */}
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {selectedElements.map((element) => {
                const availableElement = availableElements.find(
                  ae => ae.selector === element.selector
                );
                
                return (
                  <motion.div
                    key={element.selector}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      element.selected
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => toggleElement(element.selector)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {element.selected ? (
                          <CheckSquare className="h-4 w-4 text-primary" />
                        ) : (
                          <Square className="h-4 w-4 text-muted-foreground" />
                        )}
                        <div>
                          <div className="font-medium text-sm">{element.label}</div>
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <Code className="h-3 w-3" />
                            {element.selector}
                          </div>
                        </div>
                      </div>
                      {availableElement && (
                        <div className="text-right">
                          <div className="text-xs text-muted-foreground">
                            {availableElement.count} elements
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ~{Math.round(availableElement.totalTextLength / 100)}00 chars
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Preview Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Content Preview</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </CardTitle>
            <CardDescription>
              Preview of content that will be extracted from selected elements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AnimatePresence>
              {showPreview && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4"
                >
                  {hasSelection ? (
                    <div className="max-h-64 overflow-y-auto p-3 bg-muted/50 rounded-lg">
                      <pre className="text-sm whitespace-pre-wrap font-mono">
                        {previewContent || 'Generating preview...'}
                      </pre>
                    </div>
                  ) : (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Select at least one element to see a preview of the extracted content.
                      </AlertDescription>
                    </Alert>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          onClick={handleProceed}
          disabled={!hasSelection || isProcessing}
          className="min-w-32"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <ArrowRight className="h-4 w-4 mr-2" />
              Proceed with Selection
            </>
          )}
        </Button>
      </div>

      {/* Status */}
      {!hasSelection && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertCircle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            Please select at least one content element to proceed with the extraction.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
