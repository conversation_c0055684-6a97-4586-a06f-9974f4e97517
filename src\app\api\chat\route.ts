import { NextRequest, NextResponse } from 'next/server';
import enhancedDocumentService from '@/lib/enhanced-document-service';
import { apiKeyStorage, validateApiKey } from '@/lib/api-key-storage';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  sources?: Source[];
  timestamp: Date;
}

interface Source {
  document: string;
  chunk: string;
  relevance: number;
}

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check for API key from multiple sources
    const apiKey = apiKeyStorage.getApiKey() || 
                   process.env.DEEPSEEK_API_KEY || 
                   process.env.DOUBAO_API_KEY;

    if (!apiKey || apiKey === 'your_deepseek_api_key_here' || apiKey === 'your_doubao_api_key_here') {
      return NextResponse.json({
        response: "⚠️ API key is not configured. Please configure your API key using the settings panel or environment variables.\n\n🔧 **Settings Panel Available** - Click the settings icon to configure your DeepSeek/Doubao API key directly in the UI!\n\nAlternatively:\n1. Get an API key from your provider\n2. Add it to your .env.local file\n3. Restart the development server\n\n🔧 **Chinese AI Stack** - DeepSeek (reasoning) + Doubao (embeddings) for enhanced document understanding!",
        sources: [],
        timestamp: new Date().toISOString()
      });
    }

    // Validate the API key if it exists
    if (apiKey) {
      const validation = validateApiKey(apiKey);
      if (!validation.isValid) {
        return NextResponse.json({
          response: `⚠️ API key validation failed: ${validation.error}\n\nPlease check your API key in the settings panel and ensure it's correctly formatted.`,
          sources: [],
          timestamp: new Date().toISOString()
        });
      }
    }

    // Convert chat history to LlamaIndex format
    const chatHistory = (history || []).map((msg: ChatMessage) => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content,
    }));

    try {
      // Query using Enhanced Document Service with DeepSeek + Doubao
      const result = await enhancedDocumentService.query(message, chatHistory);

      return NextResponse.json({
        response: result.response,
        sources: result.sources,
        timestamp: new Date().toISOString()
      });
    } catch (queryError) {
      console.error('DeepSeek + Doubao query error:', queryError);

      // Fallback response if the enhanced service fails
      return NextResponse.json({
        response: `I apologize, but I encountered an error while processing your question: "${message}".

**Possible issues:**
- DeepSeek or Doubao API keys might be invalid or expired
- No documents have been uploaded yet
- Network connectivity issues
- Embedding generation failed

**🔧 Chinese AI Stack Status:**
- ✅ DeepSeek API configured (reasoning)
- ✅ Doubao API configured (embeddings)
- ⚠️ Error occurred during query processing

Please check your API keys and ensure you have uploaded some documents, then try again.

**Error details:** ${queryError instanceof Error ? queryError.message : 'Unknown error'}`,
        sources: [],
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Chat error:', error);
    return NextResponse.json(
      { error: 'Failed to process chat message' },
      { status: 500 }
    );
  }
}
