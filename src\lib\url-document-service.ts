// URL document ingestion service
import * as cheerio from 'cheerio';
import TurndownService from 'turndown';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import enhancedDocumentService from './enhanced-document-service';
import documentManager from './document-manager';

interface UrlIngestionOptions {
  url: string;
  username?: string;
  password?: string;
  customHeaders?: Record<string, string>;
}

interface UrlIngestionResult {
  success: boolean;
  documentId?: string;
  filename?: string;
  error?: string;
  contentPreview?: string;
  contentQuality?: {
    score: number;
    warnings: string[];
    extractedContentLength: number;
    originalContentLength: number;
  };
}

class UrlDocumentService {
  private turndownService: TurndownService;
  private readonly uploadsDir = path.join(process.cwd(), 'uploads');

  constructor() {
    // Initialize Turndown service for HTML to Markdown conversion
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full',
    });

    // Configure Turndown rules for better content extraction
    this.turndownService.addRule('removeScripts', {
      filter: ['script', 'style', 'noscript'],
      replacement: () => ''
    });

    this.turndownService.addRule('cleanWhitespace', {
      filter: (node: any) => {
        return node.nodeName === '#text' && /^\s*$/.test(node.textContent || '');
      },
      replacement: () => ''
    });

    // Enhanced table handling for better markdown conversion
    this.turndownService.addRule('improvedTable', {
      filter: 'table',
      replacement: (content: string, node: any) => {
        const rows = Array.from(node.querySelectorAll('tr'));
        if (rows.length === 0) return content;

        let markdown = '\n\n';
        let isFirstRow = true;

        rows.forEach((row: any) => {
          const cells = Array.from(row.querySelectorAll('td, th'));
          if (cells.length === 0) return;

          const cellContents = cells.map((cell: any) => {
            return cell.textContent?.trim().replace(/\|/g, '\\|') || '';
          });

          markdown += '| ' + cellContents.join(' | ') + ' |\n';

          // Add header separator for first row
          if (isFirstRow) {
            markdown += '| ' + cellContents.map(() => '---').join(' | ') + ' |\n';
            isFirstRow = false;
          }
        });

        return markdown + '\n';
      }
    });

    // Remove Confluence-specific unwanted elements
    this.turndownService.addRule('removeConfluenceClutter', {
      filter: (node: any) => {
        const element = node as Element;
        if (!element.className && !element.id) return false;

        const classStr = element.className?.toString() || '';
        const idStr = element.id?.toString() || '';

        // Confluence-specific selectors to remove
        const confluenceUnwantedPatterns = [
          'confluence-information-macro',
          'page-metadata',
          'page-restrictions',
          'likes-and-labels',
          'page-comments',
          'footer-body',
          'ia-splitter',
          'navigation',
          'breadcrumbs',
          'space-tools',
          'confluence-draft',
          'editor-toolbar',
          'rte-toolbar',
          'macro-placeholder'
        ];

        return confluenceUnwantedPatterns.some(pattern =>
          classStr.includes(pattern) || idStr.includes(pattern)
        );
      },
      replacement: () => ''
    });
  }

  /**
   * Detect if the URL is a Confluence page
   */
  private isConfluencePage(url: string, html: string): boolean {
    const confluenceIndicators = [
      'confluence',
      'atlassian',
      'wiki',
      'ajs-page-id',
      'confluence-information-macro',
      'wiki-content'
    ];

    return confluenceIndicators.some(indicator =>
      url.toLowerCase().includes(indicator) || html.includes(indicator)
    );
  }

  /**
   * Enhanced content extraction with platform-specific optimizations
   */
  private extractMainContent($: cheerio.CheerioAPI, url: string, html: string): cheerio.Cheerio<any> {
    const isConfluence = this.isConfluencePage(url, html);

    // Remove unwanted elements first
    const unwantedSelectors = [
      'script', 'style', 'noscript', 'iframe', 'object', 'embed',
      'form', 'input', 'button', 'nav', 'footer', 'aside',
      '.advertisement', '.ads', '.sidebar', '.cookie-banner',
      '.social-share', '.related-articles', '.comments-section'
    ];

    // Add Confluence-specific unwanted selectors
    if (isConfluence) {
      unwantedSelectors.push(
        '.confluence-information-macro-information',
        '.confluence-information-macro-warning',
        '.confluence-information-macro-note',
        '.page-metadata',
        '.page-restrictions-indicator',
        '.likes-and-labels-container',
        '.page-comments',
        '.footer-body',
        '.ia-splitter',
        '.aui-navgroup',
        '.breadcrumbs',
        '.space-tools-section',
        '.confluence-draft-synchronisation',
        '.editor-toolbar',
        '.rte-toolbar',
        '.macro-placeholder',
        '#navigation',
        '#footer',
        '#header',
        '.aui-header'
      );
    }

    $(unwantedSelectors.join(', ')).remove();

    // Try different content selectors based on platform
    let contentElement: cheerio.Cheerio<any>;

    if (isConfluence) {
      // Confluence-specific content selectors (in order of preference)
      const confluenceSelectors = [
        '.wiki-content',
        '#main-content .wiki-content',
        '.page-content .wiki-content',
        '#content .wiki-content',
        '.main-content',
        '#main-content',
        '.page-content',
        '#content'
      ];

      contentElement = $('body'); // fallback
      for (const selector of confluenceSelectors) {
        const element = $(selector).first();
        if (element.length > 0 && element.text().trim().length > 100) {
          contentElement = element;
          break;
        }
      }
    } else {
      // General content selectors
      const generalSelectors = [
        'main',
        'article',
        '.content',
        '.post',
        '.entry',
        '#content',
        '#main',
        '.main-content',
        '.page-content',
        '.post-content',
        '.entry-content'
      ];

      contentElement = $('body'); // fallback
      for (const selector of generalSelectors) {
        const element = $(selector).first();
        if (element.length > 0 && element.text().trim().length > 100) {
          contentElement = element;
          break;
        }
      }
    }

    return contentElement;
  }

  /**
   * Assess content quality and provide warnings
   */
  private assessContentQuality(originalHtml: string, extractedMarkdown: string): {
    score: number;
    warnings: string[];
    extractedContentLength: number;
    originalContentLength: number;
  } {
    const warnings: string[] = [];
    const extractedLength = extractedMarkdown.length;
    const originalLength = originalHtml.length;

    // Calculate text-to-markup ratio
    const textContent = extractedMarkdown.replace(/[#*`\[\]()_~]/g, '').trim();
    const textLength = textContent.length;
    const markupRatio = textLength / extractedLength;

    // Check for common quality issues
    if (extractedLength < 500) {
      warnings.push('Very short content extracted - may indicate poor content detection');
    }

    if (markupRatio < 0.6) {
      warnings.push('High markup-to-text ratio - content may contain excessive formatting or unwanted elements');
    }

    if (extractedMarkdown.includes('JavaScript') && extractedMarkdown.includes('function')) {
      warnings.push('Content appears to contain JavaScript code - may indicate incomplete script removal');
    }

    if (extractedMarkdown.includes('CSS') && extractedMarkdown.includes('{')) {
      warnings.push('Content appears to contain CSS code - may indicate incomplete style removal');
    }

    const tableCount = (extractedMarkdown.match(/\|.*\|/g) || []).length;
    if (tableCount > 20) {
      warnings.push(`High number of table rows (${Math.floor(tableCount/2)}) - consider if all tables are necessary`);
    }

    // Calculate quality score (0-100)
    let score = 100;
    score -= warnings.length * 15; // Deduct points for each warning
    score = Math.max(0, Math.min(100, score));

    return {
      score,
      warnings,
      extractedContentLength: extractedLength,
      originalContentLength: originalLength
    };
  }

  async ingestFromUrl(options: UrlIngestionOptions): Promise<UrlIngestionResult> {
    try {
      // Validate URL
      const url = new URL(options.url);
      if (!['http:', 'https:'].includes(url.protocol)) {
        return {
          success: false,
          error: 'Only HTTP and HTTPS URLs are supported'
        };
      }

      // Prepare fetch options
      const fetchOptions: RequestInit = {
        method: 'GET',
        headers: {
          'User-Agent': 'ChatDoc URL Ingestion Service/1.0',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          ...options.customHeaders
        }
      };

      // Add authentication if provided
      if (options.username && options.password) {
        const auth = Buffer.from(`${options.username}:${options.password}`).toString('base64');
        fetchOptions.headers = {
          ...fetchOptions.headers,
          'Authorization': `Basic ${auth}`
        };
      }

      console.log(`Fetching content from URL: ${options.url}`);
      
      // Fetch the webpage
      const response = await fetch(options.url, fetchOptions);
      
      if (!response.ok) {
        return {
          success: false,
          error: `Failed to fetch URL: ${response.status} ${response.statusText}`
        };
      }

      const contentType = response.headers.get('content-type') || '';
      if (!contentType.includes('text/html')) {
        return {
          success: false,
          error: `Unsupported content type: ${contentType}. Only HTML content is supported.`
        };
      }

      // Get HTML content
      const html = await response.text();
      
      if (!html || html.trim().length === 0) {
        return {
          success: false,
          error: 'No content found at the provided URL'
        };
      }

      // Parse HTML and extract meaningful content
      const $ = cheerio.load(html);

      console.log(`Processing ${this.isConfluencePage(options.url, html) ? 'Confluence' : 'general'} page: ${options.url}`);

      // Use enhanced content extraction
      const contentElement = this.extractMainContent($, options.url, html);

      // Extract title
      let title = $('title').text().trim();
      if (!title) {
        title = $('h1').first().text().trim();
      }
      if (!title) {
        title = url.hostname;
      }

      // Clean up title for filename
      const cleanTitle = title
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 100);

      // Convert HTML to Markdown
      const htmlContent = contentElement.html() || '';
      let markdownContent = this.turndownService.turndown(htmlContent);

      // Clean up markdown content
      markdownContent = markdownContent
        .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
        .replace(/^\s+|\s+$/gm, '') // Trim whitespace from lines
        .trim();

      // Assess content quality
      const contentQuality = this.assessContentQuality(htmlContent, markdownContent);

      console.log(`Content quality assessment:`, {
        score: contentQuality.score,
        warnings: contentQuality.warnings,
        extractedLength: contentQuality.extractedContentLength,
        originalLength: contentQuality.originalContentLength
      });

      // Add metadata header with quality information
      const qualityInfo = contentQuality.warnings.length > 0
        ? `\n<!-- Content Quality Warnings:\n${contentQuality.warnings.map(w => `- ${w}`).join('\n')}\n-->\n`
        : '';

      const metadata = `---
title: ${title}
source_url: ${options.url}
ingested_at: ${new Date().toISOString()}
content_type: webpage
content_quality_score: ${contentQuality.score}
is_confluence: ${this.isConfluencePage(options.url, html)}
---

# ${title}

Source: [${options.url}](${options.url})
${qualityInfo}
---

${markdownContent}`;

      // Ensure uploads directory exists
      await fs.mkdir(this.uploadsDir, { recursive: true });

      // Generate unique filename
      const documentId = uuidv4();
      const filename = `${cleanTitle}-${documentId}.md`;
      const filePath = path.join(this.uploadsDir, filename);

      // Save markdown content to file
      await fs.writeFile(filePath, metadata, 'utf-8');

      // Calculate file size
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;

      // Initialize document manager to load existing metadata
      await documentManager.initialize();

      // Add to document manager
      await documentManager.addDocument({
        id: documentId,
        filename: filename,
        originalName: `${title} (from ${url.hostname})`,
        size: fileSize,
        type: '.md',
      });

      // Process with enhanced document service
      try {
        await enhancedDocumentService.addDocument(filePath, documentId, filename, fileSize);
        console.log(`Successfully processed URL document: ${filename}`);
      } catch (processingError) {
        console.error('Failed to process URL document:', processingError);
        // Continue even if processing fails - file is still saved
      }

      return {
        success: true,
        documentId,
        filename,
        contentPreview: markdownContent.substring(0, 500) + (markdownContent.length > 500 ? '...' : ''),
        contentQuality
      };

    } catch (error) {
      console.error('URL ingestion error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async validateUrl(url: string): Promise<{ valid: boolean; error?: string }> {
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          valid: false,
          error: 'Only HTTP and HTTPS URLs are supported'
        };
      }
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid URL format'
      };
    }
  }
}

// Singleton instance
const urlDocumentService = new UrlDocumentService();

export default urlDocumentService;
export type { UrlIngestionOptions, UrlIngestionResult };
