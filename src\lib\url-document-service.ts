// URL document ingestion service
import * as cheerio from 'cheerio';
import TurndownService from 'turndown';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import enhancedDocumentService from './enhanced-document-service';
import documentManager from './document-manager';

interface UrlIngestionOptions {
  url: string;
  username?: string;
  password?: string;
  customHeaders?: Record<string, string>;
}

interface UrlIngestionResult {
  success: boolean;
  documentId?: string;
  filename?: string;
  error?: string;
  contentPreview?: string;
  contentQuality?: {
    score: number;
    warnings: string[];
    extractedContentLength: number;
    originalContentLength: number;
  };
}

class UrlDocumentService {
  private turndownService: TurndownService;
  private readonly uploadsDir = path.join(process.cwd(), 'uploads');

  constructor() {
    // Initialize Turndown service for HTML to Markdown conversion
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full',
    });

    // Configure Turndown rules for better content extraction
    this.turndownService.addRule('removeScripts', {
      filter: ['script', 'style', 'noscript'],
      replacement: () => ''
    });

    this.turndownService.addRule('cleanWhitespace', {
      filter: (node: any) => {
        return node.nodeName === '#text' && /^\s*$/.test(node.textContent || '');
      },
      replacement: () => ''
    });

    // Enhanced table handling for better markdown conversion
    this.turndownService.addRule('improvedTable', {
      filter: 'table',
      replacement: (content: string, node: any) => {
        const rows = Array.from(node.querySelectorAll('tr'));
        if (rows.length === 0) return content;

        let markdown = '\n\n';
        let isFirstRow = true;

        rows.forEach((row: any) => {
          const cells = Array.from(row.querySelectorAll('td, th'));
          if (cells.length === 0) return;

          const cellContents = cells.map((cell: any) => {
            return cell.textContent?.trim().replace(/\|/g, '\\|') || '';
          });

          markdown += '| ' + cellContents.join(' | ') + ' |\n';

          // Add header separator for first row
          if (isFirstRow) {
            markdown += '| ' + cellContents.map(() => '---').join(' | ') + ' |\n';
            isFirstRow = false;
          }
        });

        return markdown + '\n';
      }
    });

    // Remove Confluence-specific unwanted elements
    this.turndownService.addRule('removeConfluenceClutter', {
      filter: (node: any) => {
        const element = node as Element;
        if (!element.className && !element.id) return false;

        const classStr = element.className?.toString() || '';
        const idStr = element.id?.toString() || '';

        // Confluence-specific selectors to remove
        const confluenceUnwantedPatterns = [
          'confluence-information-macro',
          'page-metadata',
          'page-restrictions',
          'likes-and-labels',
          'page-comments',
          'footer-body',
          'ia-splitter',
          'navigation',
          'breadcrumbs',
          'space-tools',
          'confluence-draft',
          'editor-toolbar',
          'rte-toolbar',
          'macro-placeholder'
        ];

        return confluenceUnwantedPatterns.some(pattern =>
          classStr.includes(pattern) || idStr.includes(pattern)
        );
      },
      replacement: () => ''
    });
  }

  /**
   * Detect if the URL is a Confluence page with enhanced detection
   */
  private isConfluencePage(url: string, html: string): boolean {
    console.log('\n🔍 CONFLUENCE DETECTION:');
    
    const confluenceIndicators = [
      'confluence',
      'atlassian',
      'wiki',
      'ajs-page-id',
      'confluence-information-macro',
      'wiki-content'
    ];

    let isConfluence = false;
    const detectionResults: string[] = [];

    // Check URL for indicators
    confluenceIndicators.forEach(indicator => {
      if (url.toLowerCase().includes(indicator)) {
        detectionResults.push(`✅ URL contains "${indicator}"`);
        isConfluence = true;
      } else {
        detectionResults.push(`❌ URL does not contain "${indicator}"`);
      }
    });

    // Check HTML content for indicators
    confluenceIndicators.forEach(indicator => {
      if (html.includes(indicator)) {
        detectionResults.push(`✅ HTML contains "${indicator}"`);
        isConfluence = true;
      } else {
        detectionResults.push(`❌ HTML does not contain "${indicator}"`);
      }
    });

    // Additional specific Confluence checks
    const specificChecks = [
      { name: 'Meta generator contains "Confluence"', test: () => html.includes('<meta name="generator" content="Confluence') },
      { name: 'Contains AJS namespace', test: () => html.includes('AJS.') || html.includes('ajs-') },
      { name: 'Contains Confluence CSS classes', test: () => html.includes('confluence-') },
      { name: 'Contains wiki-content class', test: () => html.includes('class="wiki-content"') || html.includes("class='wiki-content'") },
      { name: 'Contains Atlassian domain references', test: () => html.includes('atlassian.com') || html.includes('atlassian.net') }
    ];

    specificChecks.forEach(check => {
      const result = check.test();
      detectionResults.push(`${result ? '✅' : '❌'} ${check.name}`);
      if (result) isConfluence = true;
    });

    console.log('Detection results:');
    detectionResults.forEach(result => console.log(`  ${result}`));
    console.log(`Final result: ${isConfluence ? '✅ IS CONFLUENCE' : '❌ NOT CONFLUENCE'}`);

    return isConfluence;
  }

  /**
   * Save extracted content stages for debugging (Confluence pages only)
   */
  private async saveContentStagesForDebugging(stages: {
    selectedHtml: string;
    initialMarkdown: string;
    finalMarkdown: string;
    url: string;
  }): Promise<void> {
    try {
      const debugDir = path.join(process.cwd(), 'debug');
      await fs.mkdir(debugDir, { recursive: true });
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const baseName = `confluence-content-stages-${timestamp}`;
      
      // Save selected HTML
      await fs.writeFile(
        path.join(debugDir, `${baseName}-1-selected.html`),
        `<!-- Debug: Selected HTML content from ${stages.url} -->\n<!-- Generated at: ${new Date().toISOString()} -->\n\n${stages.selectedHtml}`,
        'utf-8'
      );
      
      // Save initial markdown
      await fs.writeFile(
        path.join(debugDir, `${baseName}-2-initial.md`),
        `<!-- Debug: Initial markdown from ${stages.url} -->\n<!-- Generated at: ${new Date().toISOString()} -->\n\n${stages.initialMarkdown}`,
        'utf-8'
      );
      
      // Save final markdown
      await fs.writeFile(
        path.join(debugDir, `${baseName}-3-final.md`),
        `<!-- Debug: Final markdown from ${stages.url} -->\n<!-- Generated at: ${new Date().toISOString()} -->\n\n${stages.finalMarkdown}`,
        'utf-8'
      );
      
      console.log(`✅ Saved content stages for debugging: ${baseName}-*.{html,md}`);
    } catch (error) {
      console.error('❌ Failed to save content stages for debugging:', error);
    }
  }

  /**
   * Save raw HTML content for debugging (Confluence pages only)
   */
  private async saveRawHtmlForDebugging(html: string, url: string): Promise<void> {
    try {
      const debugDir = path.join(process.cwd(), 'debug');
      await fs.mkdir(debugDir, { recursive: true });
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `confluence-raw-content-${timestamp}.html`;
      const filePath = path.join(debugDir, filename);
      
      const debugContent = `<!-- Debug: Raw HTML content from ${url} -->
<!-- Generated at: ${new Date().toISOString()} -->

${html}`;
      
      await fs.writeFile(filePath, debugContent, 'utf-8');
      console.log(`✅ Saved raw HTML for debugging: ${filePath}`);
    } catch (error) {
      console.error('❌ Failed to save raw HTML for debugging:', error);
    }
  }

  /**
   * Enhanced content extraction with platform-specific optimizations and detailed logging
   */
  private extractMainContent($: cheerio.CheerioAPI, url: string, html: string): cheerio.Cheerio<any> {
    const isConfluence = this.isConfluencePage(url, html);
    
    console.log(`\n🔍 CONTENT EXTRACTION DEBUG for ${isConfluence ? 'CONFLUENCE' : 'GENERAL'} page: ${url}`);
    console.log('=' .repeat(80));

    // Save raw HTML for Confluence pages
    if (isConfluence) {
      this.saveRawHtmlForDebugging(html, url).catch(console.error);
    }

    // Log page structure analysis
    console.log('\n📊 PAGE STRUCTURE ANALYSIS:');
    console.log(`- Total elements: ${$('*').length}`);
    console.log(`- Body children: ${$('body > *').length}`);
    console.log(`- Script tags: ${$('script').length}`);
    console.log(`- Style tags: ${$('style').length}`);
    console.log(`- Div elements: ${$('div').length}`);
    console.log(`- Main content candidates: ${$('main, article, .content, .wiki-content, #content').length}`);

    // Remove unwanted elements first
    const unwantedSelectors = [
      'script', 'style', 'noscript', 'iframe', 'object', 'embed',
      'form', 'input', 'button', 'nav', 'footer', 'aside',
      '.advertisement', '.ads', '.sidebar', '.cookie-banner',
      '.social-share', '.related-articles', '.comments-section'
    ];

    // Add Confluence-specific unwanted selectors
    if (isConfluence) {
      unwantedSelectors.push(
        '.confluence-information-macro-information',
        '.confluence-information-macro-warning',
        '.confluence-information-macro-note',
        '.page-metadata',
        '.page-restrictions-indicator',
        '.likes-and-labels-container',
        '.page-comments',
        '.footer-body',
        '.ia-splitter',
        '.aui-navgroup',
        '.breadcrumbs',
        '.space-tools-section',
        '.confluence-draft-synchronisation',
        '.editor-toolbar',
        '.rte-toolbar',
        '.macro-placeholder',
        '#navigation',
        '#footer',
        '#header',
        '.aui-header'
      );
    }

    console.log('\n🗑️  UNWANTED ELEMENTS REMOVAL:');
    unwantedSelectors.forEach(selector => {
      const elements = $(selector);
      if (elements.length > 0) {
        console.log(`- Removing ${elements.length} elements matching "${selector}"`);
        elements.each((i, el) => {
          const $el = $(el);
          const text = $el.text().trim().substring(0, 100);
          console.log(`  └─ [${i+1}] ${$el.prop('tagName') || 'unknown'} (${text.length} chars): "${text}${text.length >= 100 ? '...' : ''}"`);
        });
      }
    });

    $(unwantedSelectors.join(', ')).remove();
    console.log(`- Remaining elements after cleanup: ${$('*').length}`);

    // Try different content selectors based on platform
    let contentElement: cheerio.Cheerio<any>;

    if (isConfluence) {
      console.log('\n🏢 CONFLUENCE CONTENT SELECTOR TESTING:');
      // Confluence-specific content selectors (in order of preference)
      const confluenceSelectors = [
        '.wiki-content',
        '#main-content .wiki-content',
        '.page-content .wiki-content',
        '#content .wiki-content',
        '.main-content',
        '#main-content',
        '.page-content',
        '#content'
      ];

      contentElement = $('body'); // fallback
      let selectorFound = false;
      
      for (const selector of confluenceSelectors) {
        const element = $(selector).first();
        const textLength = element.text().trim().length;
        const hasContent = element.length > 0 && textLength > 100;
        
        console.log(`- Testing "${selector}": ${element.length} elements found, ${textLength} text chars, valid: ${hasContent}`);
        
        if (hasContent) {
          contentElement = element;
          selectorFound = true;
          console.log(`  ✅ SELECTED: "${selector}" (${textLength} chars)`);
          // Show content hierarchy
          element.children().each((i, child) => {
            const $child = $(child);
            const childText = $child.text().trim().substring(0, 50);
            console.log(`    └─ Child ${i+1}: <${$child.prop('tagName')?.toLowerCase()}> "${childText}${childText.length >= 50 ? '...' : ''}"`);
          });
          break;
        }
      }
      
      if (!selectorFound) {
        console.log('  ⚠️  No specific Confluence selector matched, using body fallback');
      }
    } else {
      console.log('\n🌐 GENERAL CONTENT SELECTOR TESTING:');
      // General content selectors
      const generalSelectors = [
        'main',
        'article',
        '.content',
        '.post',
        '.entry',
        '#content',
        '#main',
        '.main-content',
        '.page-content',
        '.post-content',
        '.entry-content'
      ];

      contentElement = $('body'); // fallback
      let selectorFound = false;
      
      for (const selector of generalSelectors) {
        const element = $(selector).first();
        const textLength = element.text().trim().length;
        const hasContent = element.length > 0 && textLength > 100;
        
        console.log(`- Testing "${selector}": ${element.length} elements found, ${textLength} text chars, valid: ${hasContent}`);
        
        if (hasContent) {
          contentElement = element;
          selectorFound = true;
          console.log(`  ✅ SELECTED: "${selector}" (${textLength} chars)`);
          break;
        }
      }
      
      if (!selectorFound) {
        console.log('  ⚠️  No specific selector matched, using body fallback');
      }
    }

    console.log('\n📈 FINAL CONTENT ANALYSIS:');
    const finalText = contentElement.text().trim();
    console.log(`- Selected element: <${contentElement.prop('tagName')?.toLowerCase()}>`);
    console.log(`- Content length: ${finalText.length} characters`);
    console.log(`- Direct children: ${contentElement.children().length}`);
    console.log(`- Preview: "${finalText.substring(0, 200)}${finalText.length > 200 ? '...' : ''}"`);
    
    // Log content structure
    console.log('\n🏗️  CONTENT STRUCTURE:');
    contentElement.children().slice(0, 10).each((i, child) => {
      const $child = $(child);
      const tagName = $child.prop('tagName')?.toLowerCase() || 'unknown';
      const className = $child.attr('class') || '';
      const id = $child.attr('id') || '';
      const text = $child.text().trim().substring(0, 100);
      console.log(`- [${i+1}] <${tagName}>${className ? ` class="${className}"` : ''}${id ? ` id="${id}"` : ''} (${text.length} chars)`);
      console.log(`    "${text}${text.length >= 100 ? '...' : ''}"`);
    });
    
    if (contentElement.children().length > 10) {
      console.log(`... and ${contentElement.children().length - 10} more children`);
    }

    console.log('=' .repeat(80));
    
    return contentElement;
  }

  /**
   * Assess content quality and provide warnings
   */
  private assessContentQuality(originalHtml: string, extractedMarkdown: string): {
    score: number;
    warnings: string[];
    extractedContentLength: number;
    originalContentLength: number;
  } {
    const warnings: string[] = [];
    const extractedLength = extractedMarkdown.length;
    const originalLength = originalHtml.length;

    // Calculate text-to-markup ratio
    const textContent = extractedMarkdown.replace(/[#*`\[\]()_~]/g, '').trim();
    const textLength = textContent.length;
    const markupRatio = textLength / extractedLength;

    // Check for common quality issues
    if (extractedLength < 500) {
      warnings.push('Very short content extracted - may indicate poor content detection');
    }

    if (markupRatio < 0.6) {
      warnings.push('High markup-to-text ratio - content may contain excessive formatting or unwanted elements');
    }

    if (extractedMarkdown.includes('JavaScript') && extractedMarkdown.includes('function')) {
      warnings.push('Content appears to contain JavaScript code - may indicate incomplete script removal');
    }

    if (extractedMarkdown.includes('CSS') && extractedMarkdown.includes('{')) {
      warnings.push('Content appears to contain CSS code - may indicate incomplete style removal');
    }

    const tableCount = (extractedMarkdown.match(/\|.*\|/g) || []).length;
    if (tableCount > 20) {
      warnings.push(`High number of table rows (${Math.floor(tableCount/2)}) - consider if all tables are necessary`);
    }

    // Calculate quality score (0-100)
    let score = 100;
    score -= warnings.length * 15; // Deduct points for each warning
    score = Math.max(0, Math.min(100, score));

    return {
      score,
      warnings,
      extractedContentLength: extractedLength,
      originalContentLength: originalLength
    };
  }

  async ingestFromUrl(options: UrlIngestionOptions): Promise<UrlIngestionResult> {
    try {
      // Validate URL
      const url = new URL(options.url);
      if (!['http:', 'https:'].includes(url.protocol)) {
        return {
          success: false,
          error: 'Only HTTP and HTTPS URLs are supported'
        };
      }

      // Prepare fetch options
      const fetchOptions: RequestInit = {
        method: 'GET',
        headers: {
          'User-Agent': 'ChatDoc URL Ingestion Service/1.0',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          ...options.customHeaders
        }
      };

      // Add authentication if provided
      if (options.username && options.password) {
        const auth = Buffer.from(`${options.username}:${options.password}`).toString('base64');
        fetchOptions.headers = {
          ...fetchOptions.headers,
          'Authorization': `Basic ${auth}`
        };
      }

      console.log(`Fetching content from URL: ${options.url}`);
      
      // Fetch the webpage
      const response = await fetch(options.url, fetchOptions);
      
      if (!response.ok) {
        return {
          success: false,
          error: `Failed to fetch URL: ${response.status} ${response.statusText}`
        };
      }

      const contentType = response.headers.get('content-type') || '';
      if (!contentType.includes('text/html')) {
        return {
          success: false,
          error: `Unsupported content type: ${contentType}. Only HTML content is supported.`
        };
      }

      // Get HTML content
      const html = await response.text();
      
      if (!html || html.trim().length === 0) {
        return {
          success: false,
          error: 'No content found at the provided URL'
        };
      }

      // Parse HTML and extract meaningful content
      const $ = cheerio.load(html);

      console.log(`Processing ${this.isConfluencePage(options.url, html) ? 'Confluence' : 'general'} page: ${options.url}`);

      // Use enhanced content extraction
      const contentElement = this.extractMainContent($, options.url, html);

      // Extract title
      let title = $('title').text().trim();
      if (!title) {
        title = $('h1').first().text().trim();
      }
      if (!title) {
        title = url.hostname;
      }

      // Clean up title for filename
      const cleanTitle = title
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 100);

      // Convert HTML to Markdown
      const htmlContent = contentElement.html() || '';
      console.log('\n📝 MARKDOWN CONVERSION:');
      console.log(`- HTML content length: ${htmlContent.length} characters`);
      console.log(`- HTML preview (first 300 chars): "${htmlContent.substring(0, 300)}${htmlContent.length > 300 ? '...' : ''}"`);
      
      let markdownContent = this.turndownService.turndown(htmlContent);
      console.log(`- Initial markdown length: ${markdownContent.length} characters`);
      console.log(`- Initial markdown preview (first 300 chars): "${markdownContent.substring(0, 300)}${markdownContent.length > 300 ? '...' : ''}"`);

      // Clean up markdown content
      const initialMarkdown = markdownContent;
      markdownContent = markdownContent
        .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
        .replace(/^\s+|\s+$/gm, '') // Trim whitespace from lines
        .trim();
      
      console.log(`- Final markdown length: ${markdownContent.length} characters`);
      console.log(`- Cleanup operations: removed excessive line breaks, trimmed whitespace`);

      // Save content stages for Confluence pages
      const isConfluence = this.isConfluencePage(options.url, html);
      if (isConfluence) {
        this.saveContentStagesForDebugging({
          selectedHtml: htmlContent,
          initialMarkdown,
          finalMarkdown: markdownContent,
          url: options.url
        }).catch(console.error);
      }

      // Assess content quality
      const contentQuality = this.assessContentQuality(htmlContent, markdownContent);

      console.log(`\n📊 Content quality assessment:`, {
        score: contentQuality.score,
        warnings: contentQuality.warnings,
        extractedLength: contentQuality.extractedContentLength,
        originalLength: contentQuality.originalContentLength
      });

      // Add metadata header with quality information
      const qualityInfo = contentQuality.warnings.length > 0
        ? `\n<!-- Content Quality Warnings:\n${contentQuality.warnings.map(w => `- ${w}`).join('\n')}\n-->\n`
        : '';

      const metadata = `---
title: ${title}
source_url: ${options.url}
ingested_at: ${new Date().toISOString()}
content_type: webpage
content_quality_score: ${contentQuality.score}
is_confluence: ${this.isConfluencePage(options.url, html)}
---

# ${title}

Source: [${options.url}](${options.url})
${qualityInfo}
---

${markdownContent}`;

      // Ensure uploads directory exists
      await fs.mkdir(this.uploadsDir, { recursive: true });

      // Generate unique filename
      const documentId = uuidv4();
      const filename = `${cleanTitle}-${documentId}.md`;
      const filePath = path.join(this.uploadsDir, filename);

      // Save markdown content to file
      await fs.writeFile(filePath, metadata, 'utf-8');

      // Calculate file size
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;

      // Initialize document manager to load existing metadata
      await documentManager.initialize();

      // Add to document manager
      await documentManager.addDocument({
        id: documentId,
        filename: filename,
        originalName: `${title} (from ${url.hostname})`,
        size: fileSize,
        type: '.md',
      });

      // Process with enhanced document service
      try {
        await enhancedDocumentService.addDocument(filePath, documentId, filename, fileSize);
        console.log(`Successfully processed URL document: ${filename}`);
      } catch (processingError) {
        console.error('Failed to process URL document:', processingError);
        // Continue even if processing fails - file is still saved
      }

      return {
        success: true,
        documentId,
        filename,
        contentPreview: markdownContent.substring(0, 500) + (markdownContent.length > 500 ? '...' : ''),
        contentQuality
      };

    } catch (error) {
      console.error('URL ingestion error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async validateUrl(url: string): Promise<{ valid: boolean; error?: string }> {
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          valid: false,
          error: 'Only HTTP and HTTPS URLs are supported'
        };
      }
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid URL format'
      };
    }
  }
}

// Singleton instance
const urlDocumentService = new UrlDocumentService();

export default urlDocumentService;
export type { UrlIngestionOptions, UrlIngestionResult };
