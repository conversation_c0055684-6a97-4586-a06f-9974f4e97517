"use client";

import { ChatInterface } from "@/components/chat/chat-interface";
import { DocumentUpload } from "@/components/documents/document-upload";
import { UrlIngestion } from "@/components/documents/url-ingestion";
import { UploadedDocuments } from "@/components/documents/uploaded-documents";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CollapsibleDocumentSection } from "@/components/ui/collapsible-document-section";
import { SettingsModal } from "@/components/settings/settings-modal";
import { useState } from "react";

export default function Home() {
  const [documentUpdateTrigger, setDocumentUpdateTrigger] = useState(0);
  const [expandedSection, setExpandedSection] = useState<'upload' | 'url' | 'uploaded'>('upload');
  const [chatKey, setChatKey] = useState(0);

  // Function to trigger updates across components
  const handleDocumentUpdate = () => {
    setDocumentUpdateTrigger(prev => prev + 1);
  };

  // Function to handle API key updates
  const handleApiKeysUpdated = () => {
    setChatKey(prev => prev + 1); // Force chat interface to reload
  };

  // Function to handle section expansion with accordion behavior
  const handleSectionExpand = (section: 'upload' | 'url' | 'uploaded') => {
    setExpandedSection(section);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6 max-w-7xl h-screen flex flex-col">
        <div className="mb-6 text-center flex-shrink-0">
          <div className="flex justify-between items-center mb-4">
            <div></div> {/* Spacer */}
            <h1 className="text-4xl md:text-5xl font-bold text-primary">
              ChatDoc
            </h1>
            <SettingsModal onKeysUpdated={handleApiKeysUpdated} />
          </div>
          <p className="text-muted-foreground text-lg md:text-xl max-w-2xl mx-auto">
            Upload documents and chat with them using AI
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-1 min-h-0">
          {/* Document Upload Section */}
          <div className="lg:col-span-1 space-y-4 order-2 lg:order-1">
            <CollapsibleDocumentSection
              title="Upload Documents"
              description="Upload .txt or .md files to start chatting"
              icon="📄"
              isExpanded={expandedSection === 'upload'}
              onExpand={() => handleSectionExpand('upload')}
            >
              <DocumentUpload onDocumentUpdate={handleDocumentUpdate} />
            </CollapsibleDocumentSection>

            {/* URL Ingestion Section */}
            <CollapsibleDocumentSection
              title="Ingest from URL"
              description="Fetch and process documents from web URLs"
              icon="🌐"
              isExpanded={expandedSection === 'url'}
              onExpand={() => handleSectionExpand('url')}
            >
              <UrlIngestion onDocumentUpdate={handleDocumentUpdate} />
            </CollapsibleDocumentSection>

            {/* Uploaded Documents Section */}
            <CollapsibleDocumentSection
              title="Uploaded Documents"
              description="View and manage your uploaded documents"
              icon="📁"
              isExpanded={expandedSection === 'uploaded'}
              onExpand={() => handleSectionExpand('uploaded')}
            >
              <UploadedDocuments 
                onDocumentUpdate={handleDocumentUpdate} 
                refreshTrigger={documentUpdateTrigger}
              />
            </CollapsibleDocumentSection>
          </div>

          {/* Chat Interface Section */}
          <div className="lg:col-span-2 order-1 lg:order-2 flex flex-col min-h-0">
            <Card className="shadow-sm flex flex-col flex-1 min-h-0">
              <CardHeader className="flex-shrink-0 py-4">
                <CardTitle className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                    💬
                  </div>
                  Chat with Documents
                </CardTitle>
                <CardDescription>
                  Ask questions about your uploaded documents
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 p-0 min-h-0">
                <div className="h-full rounded-lg overflow-hidden">
                  <ChatInterface key={`${documentUpdateTrigger}-${chatKey}`} />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
