"use client";

import React, { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  RotateCcw, 
  ExternalLink,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { motion } from "framer-motion";

interface PagePreviewProps {
  url: string;
  title: string;
  html: string;
  onElementHover?: (selector: string | null) => void;
  onElementClick?: (selector: string) => void;
  highlightedSelectors?: string[];
  className?: string;
}

type ViewportSize = 'desktop' | 'tablet' | 'mobile';

export function PagePreview({
  url,
  title,
  html,
  onElementHover,
  onElementClick,
  highlightedSelectors = [],
  className = ""
}: PagePreviewProps) {
  const [viewportSize, setViewportSize] = useState<ViewportSize>('desktop');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const viewportSizes = {
    desktop: { width: '100%', height: '600px', icon: Monitor },
    tablet: { width: '768px', height: '600px', icon: Tablet },
    mobile: { width: '375px', height: '600px', icon: Smartphone }
  };

  useEffect(() => {
    if (iframeRef.current && html) {
      try {
        const iframe = iframeRef.current;
        const doc = iframe.contentDocument || iframe.contentWindow?.document;
        
        if (doc) {
          // Create enhanced HTML with interaction styles
          const enhancedHtml = `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <title>${title}</title>
              <style>
                /* Reset and base styles */
                * { box-sizing: border-box; }
                body { 
                  margin: 0; 
                  padding: 20px; 
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  line-height: 1.6;
                  color: #333;
                  background: #fff;
                }
                
                /* Disable interactions */
                a, button, input, textarea, select, form { 
                  pointer-events: none !important; 
                  cursor: default !important;
                }
                
                /* Hover effects for selectable elements */
                main:hover, article:hover, [role="main"]:hover,
                .content:hover, #content:hover, .post-content:hover,
                .entry-content:hover, .wiki-content:hover,
                h1:hover, h2:hover, h3:hover, h4:hover, h5:hover, h6:hover,
                p:hover, ul:hover, ol:hover, blockquote:hover,
                pre:hover, code:hover {
                  outline: 2px dashed #3b82f6 !important;
                  outline-offset: 2px !important;
                  background-color: rgba(59, 130, 246, 0.05) !important;
                  cursor: pointer !important;
                  transition: all 0.2s ease !important;
                }
                
                /* Highlighted elements */
                .chatdoc-highlighted {
                  outline: 2px solid #10b981 !important;
                  outline-offset: 2px !important;
                  background-color: rgba(16, 185, 129, 0.1) !important;
                }
                
                /* Clickable elements */
                .chatdoc-selectable {
                  cursor: pointer !important;
                  position: relative;
                }
                
                .chatdoc-selectable::before {
                  content: '';
                  position: absolute;
                  top: -2px;
                  left: -2px;
                  right: -2px;
                  bottom: -2px;
                  border: 2px dashed transparent;
                  pointer-events: none;
                  transition: border-color 0.2s ease;
                }
                
                .chatdoc-selectable:hover::before {
                  border-color: #3b82f6;
                }
                
                /* Responsive adjustments */
                img { max-width: 100%; height: auto; }
                table { width: 100%; border-collapse: collapse; }
                
                /* Hide potentially problematic elements */
                script, noscript, iframe { display: none !important; }
              </style>
            </head>
            <body>
              ${html}
              <script>
                // Add click handlers for element selection
                document.addEventListener('click', function(e) {
                  e.preventDefault();
                  e.stopPropagation();
                  
                  const target = e.target;
                  const selectors = [
                    'main', 'article', '[role="main"]',
                    '.content', '#content', '.post-content',
                    '.entry-content', '.wiki-content',
                    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                    'p', 'ul', 'ol', 'blockquote', 'pre', 'code'
                  ];
                  
                  for (const selector of selectors) {
                    if (target.matches && target.matches(selector)) {
                      window.parent.postMessage({
                        type: 'elementClick',
                        selector: selector,
                        tagName: target.tagName.toLowerCase(),
                        className: target.className,
                        id: target.id
                      }, '*');
                      break;
                    }
                  }
                });
                
                // Add hover handlers
                document.addEventListener('mouseover', function(e) {
                  const target = e.target;
                  const selectors = [
                    'main', 'article', '[role="main"]',
                    '.content', '#content', '.post-content',
                    '.entry-content', '.wiki-content',
                    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                    'p', 'ul', 'ol', 'blockquote', 'pre', 'code'
                  ];
                  
                  for (const selector of selectors) {
                    if (target.matches && target.matches(selector)) {
                      window.parent.postMessage({
                        type: 'elementHover',
                        selector: selector
                      }, '*');
                      break;
                    }
                  }
                });
                
                document.addEventListener('mouseout', function(e) {
                  window.parent.postMessage({
                    type: 'elementHover',
                    selector: null
                  }, '*');
                });
                
                // Highlight selected elements
                function highlightElements(selectors) {
                  // Remove existing highlights
                  document.querySelectorAll('.chatdoc-highlighted').forEach(el => {
                    el.classList.remove('chatdoc-highlighted');
                  });
                  
                  // Add new highlights
                  selectors.forEach(selector => {
                    try {
                      document.querySelectorAll(selector).forEach(el => {
                        el.classList.add('chatdoc-highlighted');
                      });
                    } catch (e) {
                      console.warn('Invalid selector:', selector);
                    }
                  });
                }
                
                // Listen for highlight updates
                window.addEventListener('message', function(e) {
                  if (e.data.type === 'highlight') {
                    highlightElements(e.data.selectors || []);
                  }
                });
              </script>
            </body>
            </html>
          `;

          doc.open();
          doc.write(enhancedHtml);
          doc.close();
          
          setIsLoading(false);
          setHasError(false);
        }
      } catch (error) {
        console.error('Error loading preview:', error);
        setHasError(true);
        setIsLoading(false);
      }
    }
  }, [html, title]);

  // Handle messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'elementClick' && onElementClick) {
        onElementClick(event.data.selector);
      } else if (event.data.type === 'elementHover' && onElementHover) {
        onElementHover(event.data.selector);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onElementClick, onElementHover]);

  // Update highlighted elements
  useEffect(() => {
    if (iframeRef.current && iframeRef.current.contentWindow) {
      iframeRef.current.contentWindow.postMessage({
        type: 'highlight',
        selectors: highlightedSelectors
      }, '*');
    }
  }, [highlightedSelectors]);

  const handleRefresh = () => {
    setIsLoading(true);
    setHasError(false);
    // Trigger re-render
    if (iframeRef.current) {
      iframeRef.current.src = 'about:blank';
      setTimeout(() => {
        // Re-trigger the useEffect
        setIsLoading(false);
      }, 100);
    }
  };

  const currentViewport = viewportSizes[viewportSize];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Page Preview
            </CardTitle>
            <CardDescription className="flex items-center gap-2 mt-1">
              <ExternalLink className="h-3 w-3" />
              <span className="truncate max-w-md">{url}</span>
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {/* Viewport Size Controls */}
            <div className="flex border rounded-lg overflow-hidden">
              {Object.entries(viewportSizes).map(([size, config]) => {
                const Icon = config.icon;
                return (
                  <Button
                    key={size}
                    variant={viewportSize === size ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewportSize(size as ViewportSize)}
                    className="rounded-none border-0"
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Viewport Info */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Viewport: {viewportSize}</span>
            <Badge variant="outline">
              {currentViewport.width} × {currentViewport.height}
            </Badge>
          </div>

          {/* Preview Container */}
          <div className="border rounded-lg overflow-hidden bg-gray-50">
            <motion.div
              key={viewportSize}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="flex justify-center p-4"
              style={{
                minHeight: currentViewport.height
              }}
            >
              {isLoading && (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              )}
              
              {hasError && (
                <Alert className="max-w-md">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Failed to load page preview. The page may have CORS restrictions or other loading issues.
                  </AlertDescription>
                </Alert>
              )}
              
              {!isLoading && !hasError && (
                <iframe
                  ref={iframeRef}
                  className="border-0 bg-white shadow-sm"
                  style={{
                    width: currentViewport.width,
                    height: currentViewport.height,
                    maxWidth: '100%'
                  }}
                  sandbox="allow-scripts allow-same-origin"
                  title={`Preview of ${title}`}
                />
              )}
            </motion.div>
          </div>

          {/* Instructions */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>How to select elements:</strong> Hover over different parts of the page to see selectable elements highlighted. 
              Click on any highlighted element to select it for content extraction.
            </AlertDescription>
          </Alert>
        </div>
      </CardContent>
    </Card>
  );
}
