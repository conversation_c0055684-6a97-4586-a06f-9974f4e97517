// Simplified document service using DeepSeek directly
import path from "path";
import documentManager, { type DocumentMetadata } from "./document-manager";
import { DeepSeekService } from "./deepseek-llm";

// Create DeepSeek service instance
const deepSeekService = new DeepSeekService({
  baseURL: process.env.DEEPSEEK_BASE_URL || "https://api.deepseek.com",
  model: process.env.DEEPSEEK_CHAT_MODEL || "deepseek-chat",
  temperature: 0.7,
  maxTokens: 4000,
});

// For now, we'll use a simple document storage approach
// since the LlamaIndex.TS integration is complex

interface QueryResult {
  response: string;
  sources: Array<{
    document: string;
    chunk: string;
    relevance: number;
  }>;
}

class LlamaIndexService {
  private documents: Map<string, { content: string; metadata: DocumentMetadata }> = new Map();

  async initialize() {
    try {
      // Initialize document manager
      await documentManager.initialize();
      console.log("Document service initialized with DeepSeek integration");
    } catch (error) {
      console.error("Failed to initialize document service:", error);
      throw error;
    }
  }

  async addDocument(filePath: string, documentId: string, filename: string, fileSize: number): Promise<void> {
    try {
      await this.initialize();

      // Update document status to processing
      await documentManager.updateDocumentStatus(documentId, 'processing');

      // Read the document content directly
      const fs = await import('fs/promises');
      const content = await fs.readFile(filePath, 'utf-8');

      // Store document in memory for now (in production, you'd use a proper vector database)
      this.documents.set(documentId, {
        content,
        metadata: {
          id: documentId,
          filename,
          originalName: filename,
          size: fileSize,
          uploadDate: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          type: path.extname(filename),
          status: 'indexed',
        },
      });

      // Update document status to indexed
      await documentManager.updateDocumentStatus(documentId, 'indexed');

      console.log(`Document ${filename} added to document store`);
    } catch (error) {
      console.error("Failed to add document:", error);
      await documentManager.updateDocumentStatus(documentId, 'error', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  async removeDocument(documentId: string): Promise<void> {
    try {
      await this.initialize();

      // Remove from document manager
      await documentManager.removeDocument(documentId);

      // Remove from our document store
      this.documents.delete(documentId);

      console.log(`Document ${documentId} removed from document store`);
    } catch (error) {
      console.error("Failed to remove document:", error);
      throw error;
    }
  }

  async query(message: string, _chatHistory: Array<{ role: string; content: string }> = []): Promise<QueryResult> {
    try {
      await this.initialize();

      // Get all document contents for context
      const documentContents = Array.from(this.documents.values());

      if (documentContents.length === 0) {
        return {
          response: "I don't have any documents to search through. Please upload some documents first and wait for them to be processed.",
          sources: [],
        };
      }

      // Create context from all documents
      const context = documentContents
        .map(doc => `Document: ${doc.metadata.filename}\nContent: ${doc.content}`)
        .join('\n\n---\n\n');

      // Use DeepSeek to answer the question with context
      const response = await deepSeekService.queryWithContext(message, context);

      // Create sources from the documents (simple approach)
      const sources = documentContents.map(doc => ({
        document: doc.metadata.filename,
        chunk: doc.content.substring(0, 200) + "...",
        relevance: 0.8, // Simple relevance score
      }));

      return {
        response,
        sources,
      };
    } catch (error) {
      console.error("Failed to query with DeepSeek:", error);
      throw error;
    }
  }

  async getDocuments(): Promise<DocumentMetadata[]> {
    return Array.from(this.documents.values()).map(doc => doc.metadata);
  }
}

// Singleton instance
const llamaIndexService = new LlamaIndexService();

export default llamaIndexService;
