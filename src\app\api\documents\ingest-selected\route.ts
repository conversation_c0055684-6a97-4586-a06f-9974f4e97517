import { NextRequest, NextResponse } from 'next/server';
import * as cheerio from 'cheerio';
import TurndownService from 'turndown';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import enhancedDocumentService from '@/lib/enhanced-document-service';
import documentManager from '@/lib/document-manager';

interface SelectedElement {
  selector: string;
  label: string;
  selected: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url, selectedElements, username, password, customHeaders } = body;

    // Validate required fields
    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    if (!selectedElements || !Array.isArray(selectedElements)) {
      return NextResponse.json(
        { error: 'Selected elements are required' },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return NextResponse.json(
          { error: 'Only HTTP and HTTPS URLs are supported' },
          { status: 400 }
        );
      }
    } catch {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method: 'GET',
      headers: {
        'User-Agent': 'ChatDoc URL Ingestion Service/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        ...customHeaders
      }
    };

    // Add authentication if provided
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      fetchOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Basic ${auth}`
      };
    }

    console.log(`Fetching content from URL for selected elements: ${url}`);
    
    // Fetch the webpage
    const response = await fetch(url, fetchOptions);
    
    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch URL: ${response.status} ${response.statusText}` },
        { status: 400 }
      );
    }

    const contentType = response.headers.get('content-type') || '';
    if (!contentType.includes('text/html')) {
      return NextResponse.json(
        { error: `Unsupported content type: ${contentType}. Only HTML content is supported.` },
        { status: 400 }
      );
    }

    // Get HTML content
    const html = await response.text();
    
    if (!html || html.trim().length === 0) {
      return NextResponse.json(
        { error: 'No content found at the provided URL' },
        { status: 400 }
      );
    }

    // Parse HTML
    const $ = cheerio.load(html);

    // Extract title
    let title = $('title').text().trim();
    if (!title) {
      title = $('h1').first().text().trim();
    }
    if (!title) {
      title = new URL(url).hostname;
    }

    // Extract content from selected elements
    const selectedSelectors = selectedElements
      .filter((el: SelectedElement) => el.selected)
      .map((el: SelectedElement) => el.selector);

    console.log(`Extracting content from selected selectors: ${selectedSelectors.join(', ')}`);

    let extractedContent = '';
    const processedElements = new Set<string>();

    // Process selected elements in order of priority
    for (const selector of selectedSelectors) {
      const elements = $(selector);
      
      elements.each((_, element) => {
        const $element = $(element);
        const elementHtml = $.html($element);
        const elementId = $element.attr('id') || $element.attr('class') || selector;
        
        // Avoid duplicate content
        if (!processedElements.has(elementId)) {
          processedElements.add(elementId);
          extractedContent += elementHtml + '\n';
        }
      });
    }

    if (!extractedContent.trim()) {
      return NextResponse.json(
        { error: 'No content could be extracted from selected elements' },
        { status: 400 }
      );
    }

    // Convert to markdown
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      bulletListMarker: '-',
      emDelimiter: '*'
    });

    // Configure turndown to handle common elements
    turndownService.addRule('removeComments', {
      filter: (node) => node.nodeType === 8,
      replacement: () => ''
    });

    turndownService.addRule('removeScripts', {
      filter: ['script', 'style', 'noscript'],
      replacement: () => ''
    });

    let markdownContent = turndownService.turndown(extractedContent);

    // Clean up markdown content
    markdownContent = markdownContent
      .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
      .replace(/^\s+|\s+$/gm, '') // Trim whitespace from lines
      .trim();

    if (!markdownContent || markdownContent.length < 50) {
      return NextResponse.json(
        { error: 'Extracted content is too short or empty' },
        { status: 400 }
      );
    }

    // Calculate content quality
    const contentQuality = {
      score: Math.min(100, Math.max(0, (markdownContent.length / 1000) * 50 + 50)),
      warnings: [],
      extractedContentLength: markdownContent.length,
      originalContentLength: html.length
    };

    // Clean up title for filename
    const cleanTitle = title
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 100);

    // Create metadata
    const selectedElementsInfo = selectedElements
      .filter((el: SelectedElement) => el.selected)
      .map((el: SelectedElement) => `${el.label} (${el.selector})`)
      .join(', ');

    const metadata = `---
title: ${title}
source_url: ${url}
ingested_at: ${new Date().toISOString()}
content_type: webpage_selected
selected_elements: ${selectedElementsInfo}
content_quality_score: ${contentQuality.score}
extraction_method: element_selector
---

# ${title}

Source: [${url}](${url})
Selected Elements: ${selectedElementsInfo}

---

${markdownContent}`;

    // Ensure uploads directory exists
    const uploadsDir = path.join(process.cwd(), 'uploads');
    await fs.mkdir(uploadsDir, { recursive: true });

    // Generate unique filename
    const documentId = uuidv4();
    const filename = `${cleanTitle}-selected-${documentId}.md`;
    const filePath = path.join(uploadsDir, filename);

    // Save markdown content to file
    await fs.writeFile(filePath, metadata, 'utf-8');

    // Calculate file size
    const stats = await fs.stat(filePath);
    const fileSize = stats.size;

    // Initialize document manager to load existing metadata
    await documentManager.initialize();

    // Add to document manager
    await documentManager.addDocument({
      id: documentId,
      filename: filename,
      originalName: `${title} (selected from ${new URL(url).hostname})`,
      size: fileSize,
      type: '.md',
    });

    // Process with enhanced document service
    try {
      await enhancedDocumentService.addDocument(filePath, documentId, filename, fileSize);
      console.log(`Successfully processed selected URL document: ${filename}`);
    } catch (processingError) {
      console.error('Failed to process selected URL document:', processingError);
      // Continue even if processing fails - file is still saved
    }

    return NextResponse.json({
      success: true,
      message: 'Document successfully ingested from selected elements',
      documentId,
      filename,
      contentPreview: markdownContent.substring(0, 500) + (markdownContent.length > 500 ? '...' : ''),
      contentQuality,
      selectedElements: selectedElementsInfo
    });

  } catch (error) {
    console.error('Selected URL ingestion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Selected URL Ingestion API',
    usage: 'POST with { url, selectedElements, username?, password?, customHeaders? }',
    supportedProtocols: ['http', 'https'],
    supportedContentTypes: ['text/html']
  });
}
