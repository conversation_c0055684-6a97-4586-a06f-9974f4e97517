/**
 * Client-side API key storage utility
 * Uses sessionStorage for secure, temporary storage
 */

const API_KEY_STORAGE_KEY = 'chatdoc_api_key';

export interface ApiKeyStorage {
  getApiKey(): string | null;
  setApiKey(key: string): void;
  clearApiKey(): void;
  hasApiKey(): boolean;
}

class SessionApiKeyStorage implements ApiKeyStorage {
  getApiKey(): string | null {
    try {
      if (typeof window === 'undefined') {
        return null; // Server-side rendering
      }
      return sessionStorage.getItem(API_KEY_STORAGE_KEY);
    } catch (error) {
      console.error('Error reading API key from session storage:', error);
      return null;
    }
  }

  setApiKey(key: string): void {
    try {
      if (typeof window === 'undefined') {
        return; // Server-side rendering
      }
      sessionStorage.setItem(API_KEY_STORAGE_KEY, key);
    } catch (error) {
      console.error('Error saving API key to session storage:', error);
      throw new Error('Failed to save API key');
    }
  }

  clearApiKey(): void {
    try {
      if (typeof window === 'undefined') {
        return; // Server-side rendering
      }
      sessionStorage.removeItem(API_KEY_STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing API key from session storage:', error);
    }
  }

  hasApiKey(): boolean {
    return this.getApiKey() !== null;
  }
}

// Singleton instance
export const apiKeyStorage = new SessionApiKeyStorage();

/**
 * React hook for API key management
 */
export function useApiKey() {
  const getApiKey = () => apiKeyStorage.getApiKey();
  const setApiKey = (key: string) => apiKeyStorage.setApiKey(key);
  const clearApiKey = () => apiKeyStorage.clearApiKey();
  const hasApiKey = () => apiKeyStorage.hasApiKey();

  return {
    getApiKey,
    setApiKey,
    clearApiKey,
    hasApiKey,
  };
}

/**
 * Validate API key format
 */
export function validateApiKey(key: string): { isValid: boolean; error?: string } {
  if (!key || typeof key !== 'string') {
    return { isValid: false, error: 'API key is required' };
  }

  if (key.trim().length === 0) {
    return { isValid: false, error: 'API key cannot be empty' };
  }

  if (key.length < 20) {
    return { isValid: false, error: 'API key appears to be too short' };
  }

  if (key.length > 200) {
    return { isValid: false, error: 'API key appears to be too long' };
  }

  // Check for placeholder values
  const placeholders = [
    'your_api_key_here',
    'your_deepseek_api_key_here',
    'your_doubao_api_key_here',
    'sk-placeholder',
    'api_key_placeholder'
  ];

  if (placeholders.some(placeholder => key.toLowerCase().includes(placeholder.toLowerCase()))) {
    return { isValid: false, error: 'Please replace placeholder with your actual API key' };
  }

  return { isValid: true };
}