"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert } from "@/components/ui/alert";
import { Eye, EyeOff, Key, Save, Trash2, CheckCircle, AlertCircle } from "lucide-react";

interface ApiKeySettingsProps {
  onKeysUpdated?: () => void;
}

export function ApiKeySettings({ onKeysUpdated }: ApiKeySettingsProps) {
  const [apiKey, setApiKey] = useState("");
  const [showKey, setShowKey] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const [hasExistingKey, setHasExistingKey] = useState(false);

  // Load existing API key on component mount
  useEffect(() => {
    loadExistingKey();
  }, []);

  const loadExistingKey = () => {
    try {
      const storedKey = sessionStorage.getItem('chatdoc_api_key');
      if (storedKey) {
        setApiKey(storedKey);
        setHasExistingKey(true);
        setMessage({ type: 'info', text: 'API key loaded from session storage' });
      }
    } catch (error) {
      console.error('Error loading API key:', error);
    }
  };

  const handleSaveKey = async () => {
    if (!apiKey.trim()) {
      setMessage({ type: 'error', text: 'Please enter an API key' });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      // Validate the API key format (basic check)
      if (apiKey.length < 20) {
        throw new Error('API key appears to be too short. Please check your key.');
      }

      // Store in session storage
      sessionStorage.setItem('chatdoc_api_key', apiKey);
      setHasExistingKey(true);
      setMessage({ type: 'success', text: 'API key saved successfully for this session' });

      // Notify parent component
      onKeysUpdated?.();
    } catch (error) {
      console.error('Error saving API key:', error);
      setMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : 'Failed to save API key' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearKey = () => {
    setApiKey("");
    setHasExistingKey(false);
    sessionStorage.removeItem('chatdoc_api_key');
    setMessage({ type: 'info', text: 'API key cleared from session storage' });
    onKeysUpdated?.();
  };

  const handleKeyChange = (value: string) => {
    setApiKey(value);
    if (message?.type === 'error') {
      setMessage(null);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          API Key Configuration
        </CardTitle>
        <CardDescription>
          Configure your shared DeepSeek/Doubao API key for this session. 
          The key will be stored securely in session storage and cleared when you close the browser.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {message && (
          <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
            <div className="flex items-center gap-2">
              {message.type === 'success' && <CheckCircle className="h-4 w-4" />}
              {message.type === 'error' && <AlertCircle className="h-4 w-4" />}
              {message.type === 'info' && <Key className="h-4 w-4" />}
              <span>{message.text}</span>
            </div>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="api-key">
            DeepSeek/Doubao API Key
          </Label>
          <div className="relative">
            <Input
              id="api-key"
              type={showKey ? "text" : "password"}
              value={apiKey}
              onChange={(e) => handleKeyChange(e.target.value)}
              placeholder="Enter your API key..."
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
              onClick={() => setShowKey(!showKey)}
            >
              {showKey ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            This API key will be used for both DeepSeek (reasoning) and Doubao (embeddings) services.
          </p>
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={handleSaveKey} 
            disabled={isLoading || !apiKey.trim()}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save Key'}
          </Button>
          
          {hasExistingKey && (
            <Button 
              variant="outline" 
              onClick={handleClearKey}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Clear Key
            </Button>
          )}
        </div>

        <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
          <p className="font-medium mb-1">Security Notes:</p>
          <ul className="space-y-1 text-xs">
            <li>• API key is stored in session storage and cleared when browser is closed</li>
            <li>• Key is not saved to any files or persistent storage</li>
            <li>• Refresh the page if you want to enter a new key</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}